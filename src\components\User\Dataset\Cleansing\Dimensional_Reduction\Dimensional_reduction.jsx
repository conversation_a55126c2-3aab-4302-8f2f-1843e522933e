import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import CustomSlider from "../CustomSlider";

const reductionMethods = ["PCA", "UMAP"];

const cleanUniqueId = (str) => str.replace(/-\d+$/, "");

/**
 * DimensionalReduction is a React component that allows users to select and configure
 * dimensionality reduction techniques (PCA or UMAP). It interacts with the Redux store
 * to update selected options and features dynamically.
 *
 * @component
 * @returns {JSX.Element} The rendered UI for selecting and configuring dimensionality reduction.
 *
 * @example
 * <DimensionalReduction />
 */
const DimensionalReduction = () => {
  // Redux selectors for feature metadata
  const features = useSelector((state) => state.upload.features);

  // Numerical items from feature metadata
  const numericalItems = (features?.numerical?.items || []).map((f) => ({
    ...f,
  }));

  // Local state for form control
  const [selectedMethod, setSelectedMethod] = useState("PCA");
  const [componentInput, setComponentInput] = useState(2); // number of components for PCA
  const [whiteningChecked, setWhiteningChecked] = useState(false); // apply whitening in PCA
  const [umapNeighborsInput, setUmapNeighborsInput] = useState(15); // number of neighbors for UMAP
  const [umapMinDistInput, setUmapMinDistInput] = useState(0.1); // minimum distance for UMAP
  const [outputDimInput, setOutputDimInput] = useState(2); // output dimensions for UMAP
  const [selectedFeatures, setSelectedFeatures] = useState(
    []
  ); // numerical features selected for reduction
  const [error, setError] = useState(""); // error message for validation

  /**
   * Effect hook that validates parameters.
   */
  useEffect(() => {
    let hasError = false;
    const maxOutputDim =
      selectedFeatures.length > 1 ? selectedFeatures.length : 2;

    if (selectedMethod === "PCA") {
      if (componentInput < 1) {
        setError(`Number of components must be at least 1`);
        hasError = true;
      } else if (componentInput > numericalItems.length) {
        setError(
          `Number of components must be between 1 and ${numericalItems.length}`
        );
        hasError = true;
      }
    }

    if (selectedMethod === "UMAP") {
      if (outputDimInput < 1) {
        setError(`Output dimension must be at least 1`);
        hasError = true;
      } else if (outputDimInput > maxOutputDim) {
        setError(`Output dimension must be between 1 and ${maxOutputDim}`);
        hasError = true;
      }
    }
  }, [
    selectedMethod,
    componentInput,
    whiteningChecked,
    umapNeighborsInput,
    umapMinDistInput,
    outputDimInput,
    selectedFeatures,
    numericalItems.length,
  ]);

  /**
   * Handles changing dimensionality reduction method and resets relevant form state.
   * @param {string} value - The selected method name ("PCA" or "UMAP").
   */
  const handleMethodChange = (value) => {
    setError("");
    setSelectedMethod(value);
    setComponentInput(2);
    setWhiteningChecked(false);
    setUmapNeighborsInput(15);
    setUmapMinDistInput(0.1);
    setOutputDimInput(2);
    setSelectedFeatures([]);
  };

  /**
   * Toggles a feature in the list of selected numerical features.
   * @param {string} id - Feature ID to toggle.
   */
  const handleFeatureToggle = (rawId) => {
    const updated = selectedFeatures.includes(rawId)
      ? selectedFeatures.filter((f) => f !== rawId)
      : [...selectedFeatures, rawId];

    setSelectedFeatures(updated);
  };

  const maxOutputDimensions =
    selectedFeatures.length > 1 ? selectedFeatures.length : 2;
  const showUmapDimensions =
    selectedMethod === "UMAP" && selectedFeatures.length > 0;

  return (
    <div className="mx-4 max-w-7xl">
      <div className="p-8 bg-gray-50 rounded-2xl shadow-lg border border-gray-200">
        <h2 className="text-2xl font-bold text-gray-800 mb-6 border-b pb-4">
          Dimensionality Reduction
        </h2>

        {/* Dropdown to select reduction method */}
        <div className="mb-6">
          <label className="block text-lg font-semibold text-gray-700 mb-3">
            Select Reduction Method
          </label>
          <div className="flex gap-4">
            {reductionMethods.map((option) => (
              <button
                key={option}
                className={`px-6 py-3 rounded-full text-lg font-semibold transition-colors duration-300 ${
                  selectedMethod === option
                    ? "bg-blue-600 text-white shadow-lg"
                    : "bg-white text-blue-600 border border-blue-600 hover:bg-blue-50"
                }`}
                onClick={() => handleMethodChange(option)}
              >
                {option}
              </button>
            ))}
          </div>
        </div>

        {/* PCA configuration */}
        {selectedMethod === "PCA" && (
          <div className="mt-4 p-6 bg-white rounded-lg shadow-sm border border-gray-200 space-y-4">
            <h3 className="text-xl font-bold text-gray-800">PCA Parameters</h3>
            <div>
              <label className="font-semibold block text-gray-700 mb-2">
                Number of principal components:
              </label>
              <input
                type="number"
                min="1"
                max={numericalItems.length}
                value={componentInput}
                onChange={(e) =>
                  setComponentInput(parseInt(e.target.value || "0", 10))
                }
                className="w-40 px-4 py-2 border border-gray-300 rounded-md bg-white text-center 
                font-medium text-lg transition duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                checked={whiteningChecked}
                onChange={(e) => setWhiteningChecked(e.target.checked)}
                className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label className="text-gray-700">Apply Whitening</label>
            </div>
          </div>
        )}

        {/* UMAP configuration */}
        {selectedMethod === "UMAP" && (
          <div className="mt-4 p-6 bg-white rounded-lg shadow-sm border border-gray-200 space-y-4">
            <h3 className="text-xl font-bold text-gray-800">UMAP Parameters</h3>
            <div>
              <h2 className="font-bold mt-6 mb-2 text-lg">
                Number of Neighbors:
              </h2>
              <CustomSlider
                min={5}
                max={50}
                step={1}
                defaultValue={umapNeighborsInput}
                onChange={setUmapNeighborsInput}
              />
            </div>

            <div>
              <h2 className="font-bold mt-6 mb-2 text-lg">Minimum Distance:</h2>
              <CustomSlider
                min={0}
                max={0.99}
                step={0.01}
                defaultValue={umapMinDistInput}
                onChange={setUmapMinDistInput}
              />
            </div>

            {showUmapDimensions && (
              <div>
                <label className="font-semibold block text-gray-700 mb-2">
                  Output Dimensions (Min: 2, Max: {maxOutputDimensions}):
                </label>
                <input
                  type="number"
                  min="2"
                  max={maxOutputDimensions}
                  value={outputDimInput}
                  onChange={(e) =>
                    setOutputDimInput(parseInt(e.target.value || "0", 10))
                  }
                  className="w-40 px-4 py-2 border border-gray-300 rounded-md bg-white text-center 
                  font-medium text-lg transition duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            )}
          </div>
        )}

        {/* Feature selection */}
        {numericalItems.length > 0 && (
          <div className="mt-6">
            <h3 className="text-xl font-bold text-gray-800 mb-4">
              Select Features to Reduce
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {numericalItems.map((item) => (
                <div
                  key={item.id}
                  className="border p-4 rounded-lg bg-gray-100 shadow-sm"
                >
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      id={item.id}
                      checked={selectedFeatures.includes(item.id)}
                      onChange={() => handleFeatureToggle(item.id)}
                    />
                    <label
                      htmlFor={item.id}
                      className="text-gray-700 font-medium"
                    >
                      {item.feature}
                    </label>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Validation error */}
        {error && <p className="text-red-500 text-sm mt-4">{error}</p>}
      </div>
    </div>
  );
};

export default DimensionalReduction;
